import streamlit as st
import psycopg2
import pandas as pd
import os
from dotenv import load_dotenv
import warnings
from tqdm import tqdm
from datetime import date, datetime
from io import BytesIO
import time

# Load environment variables
load_dotenv()
warnings.filterwarnings("ignore")

def get_db_params(db_list=None):
    """
    Get database parameters for specified databases or all if none specified.
    db_list: List of database names e.g. ['29003', 'kr19007']
    """
    db_configs = {
        '04': ['29003', '29004'],
        '05': ['59000', '79000'],
        '06': ['kr19007', 'kr19008'],
        '07': ['hg49016', 'ga49007', '49060', '49061'],
        '08': ['29009', '29010']
    }

    # Get all db params
    all_params = {
        db: {
            "host": os.getenv(f"DB_HOST_{host}"),
            "port": os.getenv("DB_PORT"),
            "database": os.getenv(f"DB_NAME_{db}"),
            "user": os.getenv("DB_USER"),
            "password": os.getenv("DB_PASSWORD")
        }
        for host, dbs in db_configs.items()
        for db in dbs
    }

    # Return only specified dbs if db_list provided
    if db_list:
        return {k: v for k, v in all_params.items() if k in db_list}
    return all_params

def remove_letters(items):
    """Extract digits from warehouse IDs for SQL IN clause"""
    # Extract digits from each item
    digits_only = (''.join(char for char in item if char.isdigit()) for item in items)

    # Convert to list first
    digits_list = list(digits_only)

    # If there's only one element, return it without a trailing comma in the SQL
    if len(digits_list) == 1:
        return f"('{digits_list[0]}')"
    else:
        # Otherwise return a proper tuple format for SQL IN clause
        return str(tuple(digits_list))

def build_wlm_query(start_date, end_date, selected_dcs, event_type, user_class):
    """Build dynamic SQL query based on filter selections"""

    # Base query structure
    query = f"""
    SELECT *
    FROM mwlm.wlm_transaction wt
    WHERE shift_date >= TO_DATE('{start_date}', 'dd-mm-yyyy')
        AND shift_date <= TO_DATE('{end_date}', 'dd-mm-yyyy')
        AND warehouse_id IN {remove_letters(selected_dcs)}
    """

    # Add event type filter
    if event_type == "BOTH":
        query += "    AND event_type IN ('ACTUAL', 'ESTIMATED')\n"
    else:
        query += f"    AND event_type IN ('{event_type}')\n"

    # Add user class filter if not default
    if user_class != "default":
        query += f"    AND user_class IN ('{user_class}')\n"

    return query

def to_csv(df):
    """Convert DataFrame to CSV for download"""
    return df.to_csv(index=False).encode('utf-8')

def wlm_data_page():
    """WLM Data Downloader page with modern dark theme dashboard"""

    # Page configuration and styling
    st.markdown("""
    <style>
    .main-header {
        font-size: 2.5rem;
        font-weight: 700;
        color: #FFFFFF;
        text-align: center;
        margin-bottom: 2rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .filter-container {
        background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
        padding: 1.5rem;
        border-radius: 15px;
        border: 1px solid #404040;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .filter-header {
        font-size: 1.2rem;
        font-weight: 600;
        color: #E0E0E0;
        margin-bottom: 1rem;
        border-bottom: 2px solid #404040;
        padding-bottom: 0.5rem;
    }

    .metric-container {
        background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 100%);
        padding: 1rem;
        border-radius: 10px;
        border: 1px solid #505050;
        text-align: center;
        margin: 0.5rem 0;
    }

    .stButton > button {
        background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255,107,107,0.3);
    }

    .stButton > button:hover {
        background: linear-gradient(135deg, #FF5252 0%, #FF7979 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255,107,107,0.4);
    }

    .stDownloadButton > button {
        background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(78,205,196,0.3);
    }

    .stDownloadButton > button:hover {
        background: linear-gradient(135deg, #45B7B8 0%, #3D8B85 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(78,205,196,0.4);
    }
    </style>
    """, unsafe_allow_html=True)

    # Main header
    st.markdown('<h1 class="main-header">🏭 WLM Data Dashboard</h1>', unsafe_allow_html=True)

    # Available DCs
    available_dcs = ['29003', '29004', '59000', '79000', 'kr19007', 'kr19008', '49060', '49061', '29009', '29010']

    # Create filter container
    with st.container():
        st.markdown('<div class="filter-container">', unsafe_allow_html=True)
        st.markdown('<div class="filter-header">📊 Query Filters</div>', unsafe_allow_html=True)

        # Create three columns for filters
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            st.markdown("**🏢 Distribution Centers**")
            selected_dcs = st.multiselect(
                "Select DCs",
                options=available_dcs,
                default=available_dcs[:3],  # Default to first 3 DCs
                help="Choose one or more distribution centers to query",
                label_visibility="collapsed"
            )

        with col2:
            st.markdown("**📈 Event Type**")
            event_type = st.radio(
                "Event Type",
                options=["ACTUAL", "ESTIMATED", "BOTH"],
                index=0,
                help="Select the type of events to include",
                label_visibility="collapsed"
            )

        with col3:
            st.markdown("**👤 User Class**")
            user_class = st.selectbox(
                "User Class",
                options=["default", "FLT"],
                index=0,
                help="Filter by user class (default = no filter)",
                label_visibility="collapsed"
            )

        # Date range selector
        st.markdown("**📅 Date Range**")
        col_start, col_end = st.columns(2)

        with col_start:
            start_date = st.date_input(
                "Start Date",
                value=date.today(),
                help="Select the start date for the query"
            )

        with col_end:
            end_date = st.date_input(
                "End Date",
                value=date.today(),
                help="Select the end date for the query"
            )

        st.markdown('</div>', unsafe_allow_html=True)

    # Validation and query execution section
    if not selected_dcs:
        st.warning("⚠️ Please select at least one Distribution Center")
        return

    if start_date > end_date:
        st.error("❌ Start date cannot be after end date")
        return

    # Display current filter summary
    with st.expander("🔍 Current Filter Summary", expanded=False):
        st.write(f"**Selected DCs:** {', '.join(selected_dcs)}")
        st.write(f"**Event Type:** {event_type}")
        st.write(f"**User Class:** {user_class}")
        st.write(f"**Date Range:** {start_date.strftime('%d-%m-%Y')} to {end_date.strftime('%d-%m-%Y')}")

        # Show generated SQL query
        sample_query = build_wlm_query(
            start_date.strftime('%d-%m-%Y'),
            end_date.strftime('%d-%m-%Y'),
            selected_dcs,
            event_type,
            user_class
        )
        st.code(sample_query, language='sql')

    # Center the execute button
    col1, col2, col3 = st.columns([1, 1, 1])

    with col2:
        execute_query = st.button(
            "🚀 Execute Query",
            type="primary",
            use_container_width=True
        )

    # Query execution and results
    if execute_query:
        try:
            # Get database parameters for selected DCs
            db_params = get_db_params(selected_dcs)

            if not db_params:
                st.error("❌ No database configuration found for selected DCs")
                return

            # Build the query
            query = build_wlm_query(
                start_date.strftime('%d-%m-%Y'),
                end_date.strftime('%d-%m-%Y'),
                selected_dcs,
                event_type,
                user_class
            )

            # Execute query with progress tracking
            all_data = pd.DataFrame()

            # Create progress bar
            progress_bar = st.progress(0)
            status_text = st.empty()

            total_dbs = len(db_params)

            for i, (db_name, params) in enumerate(db_params.items()):
                try:
                    status_text.text(f"Querying database: {db_name}...")

                    # Connect and execute query
                    with psycopg2.connect(**params) as conn:
                        df = pd.read_sql_query(query, conn)
                        all_data = pd.concat([all_data, df], ignore_index=True)

                    # Update progress
                    progress_bar.progress((i + 1) / total_dbs)

                except Exception as e:
                    st.warning(f"⚠️ Error querying {db_name}: {str(e)}")
                    continue

            # Clear progress indicators
            progress_bar.empty()
            status_text.empty()

            if len(all_data) == 0:
                st.warning("⚠️ No data found for the selected criteria")
                return

            # Display results
            st.success(f"✅ Query completed successfully! Found {len(all_data):,} records")

            # Show data summary metrics
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.markdown(
                    f'<div class="metric-container"><h3>{len(all_data):,}</h3><p>Total Records</p></div>',
                    unsafe_allow_html=True
                )

            with col2:
                unique_warehouses = all_data['warehouse_id'].nunique() if 'warehouse_id' in all_data.columns else 0
                st.markdown(
                    f'<div class="metric-container"><h3>{unique_warehouses}</h3><p>Warehouses</p></div>',
                    unsafe_allow_html=True
                )

            with col3:
                date_range_days = (end_date - start_date).days + 1
                st.markdown(
                    f'<div class="metric-container"><h3>{date_range_days}</h3><p>Days Queried</p></div>',
                    unsafe_allow_html=True
                )

            with col4:
                data_size_mb = round(all_data.memory_usage(deep=True).sum() / 1024 / 1024, 2)
                st.markdown(
                    f'<div class="metric-container"><h3>{data_size_mb} MB</h3><p>Data Size</p></div>',
                    unsafe_allow_html=True
                )

            # Display data table
            st.markdown("### 📋 Query Results")

            # Show first few rows with option to see more
            display_rows = st.slider("Rows to display", min_value=10, max_value=min(1000, len(all_data)), value=100)
            st.dataframe(
                all_data.head(display_rows),
                use_container_width=True,
                height=400
            )

            if len(all_data) > display_rows:
                st.info(f"Showing first {display_rows} rows of {len(all_data):,} total records. Use CSV download to get all data.")

            # Download section
            st.markdown("### 💾 Download Data")

            col1, col2 = st.columns(2)

            with col1:
                # CSV download
                csv_data = to_csv(all_data)
                filename = f"wlm_data_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.csv"

                st.download_button(
                    label="📥 Download as CSV",
                    data=csv_data,
                    file_name=filename,
                    mime="text/csv",
                    use_container_width=True
                )

            with col2:
                # Excel download
                excel_data = BytesIO()
                with pd.ExcelWriter(excel_data, engine='xlsxwriter') as writer:
                    all_data.to_excel(writer, sheet_name='WLM_Data', index=False)
                excel_data.seek(0)

                filename_excel = f"wlm_data_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.xlsx"

                st.download_button(
                    label="📊 Download as Excel",
                    data=excel_data.getvalue(),
                    file_name=filename_excel,
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    use_container_width=True
                )

        except Exception as e:
            st.error(f"❌ An error occurred: {str(e)}")
            st.exception(e)
