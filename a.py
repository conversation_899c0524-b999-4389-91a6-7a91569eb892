import psycopg2
import pandas as pd
import os
from dotenv import load_dotenv
import warnings
from tqdm import tqdm
import polars as pl
import pyodbc



warnings.filterwarnings("ignore")
pd.set_option("display.max_columns", None)
pd.set_option("display.max_colwidth", None)

load_dotenv()

start = '24-06-2025'
end = '24-06-2025'
missing_products = True

def get_db_params(db_list=None):
   """
   Get database parameters for specified databases or all if none specified.
   db_list: List of database names e.g. ['29003', 'kr19007']
   """
   db_configs = {
       '04': ['29003', '29004'],
       '05': ['59000', '79000'], 
       '06': ['kr19007', 'kr19008'],
       '07': ['hg49016', 'ga49007', '49060', '49061'],
       '08': ['29009', '29010']
   }
   
   # Get all db params
   all_params = {
       db: {
           "host": os.getenv(f"DB_HOST_{host}"),
           "port": os.getenv("DB_PORT"),
           "database": os.getenv(f"DB_NAME_{db}"),
           "user": os.getenv("DB_USER"),
           "password": os.getenv("DB_PASSWORD")
       }
       for host, dbs in db_configs.items()
       for db in dbs
   }
   
   # Return only specified dbs if db_list provided
   if db_list:
       return {k: v for k, v in all_params.items() if k in db_list}
   return all_params

# Usage examples:
db_params = get_db_params(['kr19007', 'kr19008']) # '29003', '29004', '59000', '79000', 'kr19007', 'kr19008','49060', '49061','29009', '29010'
# Or
# db_params = get_db_params() # Get all DBs

def remove_letters(items):
    # Extract digits from each item
    digits_only = (''.join(char for char in item if char.isdigit()) for item in items)
    
    # Convert to list first
    digits_list = list(digits_only)
    
    # If there's only one element, return it without a trailing comma in the SQL
    if len(digits_list) == 1:
        return f"('{digits_list[0]}')"
    else:
        # Otherwise return a proper tuple format for SQL IN clause
        return str(tuple(digits_list))




# query = """

# select

# *

#   from mwlm.wlm_transaction wt
#  			where shift_date >= TO_DATE('12-02-2025,','dd-mm-yyyy')
#  	and shift_date <= to_date('12-02-2025','dd-mm-yyyy')
#     --and process_name in ('PBL_CASE_PICK', 'PBS_CASE_PICK')
#     --and process_name = 'FLT'
#     --and event_type in ('ACTUAL')
    

#  	--and container_id in ('00000190070321656284')
#  	--and user_id in ('SZOLOTUK','TESCO09')
#  	--and user_id in ('C4002268')
#  	--and elm_group_code in ('CASE_PICK_PREP')
#     --and elm_group_code in ('CASE_PICK_PREP')
#  	--and elm_group_code in ('MRG_CONF','MRG_START')
#  	and elm_group_code in ('BULK_DROP_OFF','PBS_UNIT_PICK','PBS_UNIT_SHORT', 'PBS_CASE_SHORT')


# """


# #ALL
# query = f"""
# SELECT *
# FROM mwlm.wlm_transaction wt
# WHERE shift_date >= TO_DATE('{start}', 'dd-mm-yyyy')
#     AND shift_date <= TO_DATE('{end}', 'dd-mm-yyyy')
#     AND warehouse_id IN {remove_letters(tuple(db_params.keys()))}
#     AND event_type in ('ACTUAL')
#     --AND from_location_id ~ '^[A-Z]{2}[0-9]_[0-9]{3}$' 
#     --AND to_location_id ~ '^[0-9]{3}$'  -- Contains exactly 3 digits
#     --AND event_type IN ('ESTIMATED')
#     --AND process_name in ('PBL_CASE_PICK', 'PBL')
#     AND elm_group_code in ('PBL_CASE_PICK')

# """


#blanks item_master_uda8 products
query = f"""
SELECT
    depot_country_code as country,
     warehouse_id as DC_id,
       facility_id as DC_name,
       product_number as tpnb,
       product_description as product_name
FROM mwlm.wlm_transaction wt
WHERE shift_date >= TO_DATE('{start}', 'dd-mm-yyyy')
    AND shift_date <= TO_DATE('{end}', 'dd-mm-yyyy')
    AND warehouse_id IN {remove_letters(tuple(db_params.keys()))}
    --AND event_type in ('ACTUAL')
    AND elm_group_code in ('PBL_CASE_PICK')
    AND (item_master_uda8 IS NULL OR item_master_uda8 = '')
    AND product_number != ''
GROUP BY depot_country_code, warehouse_id, facility_id, product_number, product_description
"""


# # Received cases and pallet
# query = f"""
# SELECT 

# warehouse_id as DC_id,
# facility_id as DC_name,
# elm_group_code as elm_group_code,
# SUM(case_quantity) as received_cases,
# SUM(pallet_quantity) as pallet_quantity,
# shift_date as date

# FROM mwlm.wlm_transaction wt
# WHERE shift_date >= TO_DATE('{start}', 'dd-mm-yyyy')
#     AND shift_date <= TO_DATE('{end}', 'dd-mm-yyyy')
#     AND event_type in ('ACTUAL')
#     AND warehouse_id IN {remove_letters(tuple(db_params.keys()))}
#     AND process_name in ('RECEIVING')
#     AND elm_group_code in ('BLKRCV')
    
# GROUP BY warehouse_id,facility_id, shift_date, elm_group_code

# """



# # Receiving hours
# query = f"""
# SELECT 

# warehouse_id as DC_id,
# facility_id as DC_name,
# elm_group_code as elm_group_code,
# SUM(activity_duration)/3600 as receiving_hours,
# shift_date as date

# FROM mwlm.wlm_transaction wt
# WHERE shift_date >= TO_DATE('{start}', 'dd-mm-yyyy')
#     AND shift_date <= TO_DATE('{end}', 'dd-mm-yyyy')
#     AND event_type in ('ACTUAL')
#     AND warehouse_id IN {remove_letters(tuple(db_params.keys()))}
#     AND process_name in ('RECEIVING')
    
# GROUP BY warehouse_id,facility_id, shift_date, elm_group_code

# """



# # Receiving cases
# query = f"""
# SELECT 

# warehouse_id as DC_id,
# facility_id as DC_name,
# elm_group_code as elm_group_code,
# SUM(case_quantity) as receiving_cases,
# shift_date as date

# FROM mwlm.wlm_transaction wt
# WHERE shift_date >= TO_DATE('{start}', 'dd-mm-yyyy')
#     AND shift_date <= TO_DATE('{end}', 'dd-mm-yyyy')
#     AND warehouse_id IN {remove_letters(tuple(db_params.keys()))}
#     AND event_type in ('ACTUAL')
#     AND elm_group_code in ('PBL_CASE_PICK')
    
# GROUP BY warehouse_id,facility_id, shift_date, elm_group_code

# """




# #number of containers PBL
# query = f"""
# SELECT 

# warehouse_id as DC_id,
# facility_id as DC_name,
# elm_group_code as elm_group_code,
# count(distinct(container_id)) as nr_of_containers_PBL,
# shift_date as date

# FROM mwlm.wlm_transaction wt
# WHERE shift_date >= TO_DATE('{start}', 'dd-mm-yyyy')
#     AND shift_date <= TO_DATE('{end}', 'dd-mm-yyyy')
#     AND warehouse_id IN {remove_letters(tuple(db_params.keys()))}
#     AND event_type in ('ACTUAL')
#     AND process_name in ('PBL_CASE_PICK', 'PBL')
#     AND elm_group_code in ('PBL_PICK_PREP')
#     AND container_id IS NOT NULL
    
# GROUP BY warehouse_id,facility_id, shift_date, elm_group_code

# """




# # PBL_LAD_UNLADEN_DISTANCE
# query = f"""
# SELECT
#       warehouse_id,
#       facility_id as DC_name,
#       elm_group_code,
#       CASE 
#           WHEN elm_group_code = 'PBL_LAD_TRAVEL' THEN 'LAD_TRAVEL'
#           ELSE 'UNLADEN_TRAVEL'
#       END as travel_type,
#       SUM(distance_traveled) as SUM_DISTANCE,
#       shift_date as date
# FROM mwlm.wlm_transaction wt
# WHERE shift_date >= TO_DATE('{start}', 'dd-mm-yyyy')
#     AND shift_date <= TO_DATE('{end}', 'dd-mm-yyyy')
#     AND event_type in ('ACTUAL')
#     AND warehouse_id IN {remove_letters(tuple(db_params.keys()))}
#     AND elm_group_code IN ('PBL_LAD_TRAVEL', 'PBL_UNLAD_TRAVEL', 'UNLADEN_TRAVEL', 'PBL_ASSIGNMENT_END')
#     AND distance_traveled IS NOT NULL
#     AND distance_traveled != 0
# GROUP BY warehouse_id, elm_group_code, facility_id, shift_date
# """


# # PBL picks nr of tpns 
# query = f"""
# SELECT
#       warehouse_id,
#       facility_id as DC_name,
#       elm_group_code,
#       COUNT(DISTINCT(CONCAT(shift_date, store_number, product_number))) as nr_of_tpns_PBL,
#       COUNT(DISTINCT(store_number)) as pallet_store_stop_PBL,
#       shift_date as date
# FROM mwlm.wlm_transaction wt
# WHERE shift_date >= TO_DATE('{start}', 'dd-mm-yyyy')
#     AND shift_date <= TO_DATE('{end}', 'dd-mm-yyyy')
#     AND event_type in ('ACTUAL')
#     AND warehouse_id IN {remove_letters(tuple(db_params.keys()))}
#     AND elm_group_code IN ('PBL_CASE_PICK')
#     AND store_number IS NOT NULL
#     AND product_number IS NOT NULL
# GROUP BY warehouse_id, elm_group_code, facility_id, shift_date
# """



# PBL_containers_AVG
# query = f"""
# SELECT
#       warehouse_id,
#       AVG(weekly_distinct_containers) as AVG_WEEKLY_DISTINCT_containers
# FROM (
#     SELECT
#           warehouse_id,
#           TO_CHAR(shift_date, 'YYYY-IW') as week_year,  -- ISO week format
#           COUNT(DISTINCT container_id) as weekly_distinct_containers
#     FROM mwlm.wlm_transaction wt
# WHERE shift_date >= TO_DATE('{start}', 'dd-mm-yyyy')
#     AND shift_date <= TO_DATE('{end}', 'dd-mm-yyyy')
#         AND warehouse_id IN {remove_letters(tuple(db_params.keys()))}
#         AND elm_group_code IN ('PBL_PICK_PREP')
#     GROUP BY warehouse_id, TO_CHAR(shift_date, 'YYYY-IW')
# ) weekly_data
# GROUP BY warehouse_id
# """



# query = f"""
# SELECT
#       warehouse_id,
#       COUNT(DISTINCT container_id) / 3 as AVG_WEEKLY_DISTINCT_containers
# FROM mwlm.wlm_transaction wt
# WHERE shift_date >= TO_DATE('01-05-2025', 'dd-mm-yyyy')  
#     AND shift_date <= TO_DATE('21-05-2025', 'dd-mm-yyyy')
#     AND warehouse_id IN {remove_letters(tuple(db_params.keys()))}
#     AND elm_group_code IN ('PBL_PICK_PREP')
# GROUP BY warehouse_id
# """


# query = """

# WITH date_with_week AS (
#     SELECT 
#         warehouse_id,
#         shift_date,
#         CASE 
#             WHEN shift_date BETWEEN DATE '2024-03-01' AND DATE '2024-03-03' THEN 1
#             ELSE FLOOR((shift_date - DATE '2024-03-04') / 7) + 2
#         END as week_number,
#         case_quantity
#     FROM mwlm.wlm_transaction wt
#     WHERE shift_date >= TO_DATE('01-03-2024','dd-mm-yyyy')
#         AND shift_date <= to_date('24-11-2024','dd-mm-yyyy')
#         AND event_type in ('ACTUAL')
#         AND elm_group_code in ('PBL_CASE_PICK','PBS_CASE_SHORT','PBS_CASE_PICK',
#                              'BULK_DROP_OFF','PBS_UNIT_PICK','PBS_UNIT_SHORT')
# )
# SELECT 
#     warehouse_id,
#     week_number,
#     SUM(case_quantity) as total_case_quantity
# FROM date_with_week
# GROUP BY warehouse_id, week_number
# ORDER BY warehouse_id, week_number;


# """

a = pd.DataFrame()






with tqdm(db_params.items(), desc="Processing databases", unit="db") as pbar:
    for db_name, params in pbar:
        with psycopg2.connect(**params) as conn:
            pbar.set_description(f"Processing {db_name}")
            df = pd.read_sql_query(query, conn)
            a = pd.concat([a, df])
            
            
            
# def get_data_with_progress(query, conn, chunksize=1000):
#     # Először lekérjük a teljes sorok számát
#     count_query = f"SELECT COUNT(*) FROM ({query}) as sub"
#     total_rows = pd.read_sql_query(count_query, conn).iloc[0, 0]
    
#     # Chunk-okban olvassuk az adatokat progress barral
#     chunks = []
#     with tqdm(total=total_rows, desc="Reading data", unit="rows") as pbar:
#         for chunk in pd.read_sql_query(query, conn, chunksize=chunksize):
#             chunks.append(chunk)
#             pbar.update(len(chunk))
    
#     return pd.concat(chunks) if chunks else pd.DataFrame()

# # Használat:
# a = pd.DataFrame()
# for db_name, params in db_params.items():
#     with psycopg2.connect(**params) as conn:
#         print(f"Processing {db_name}")
#         df = get_data_with_progress(query, conn)
#         a = pd.concat([a, df])


def missing_products(a):
    print(f"Function start: Processing {len(a)} input rows")
    
    conn = pyodbc.connect(
        "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
    )
    cursor = conn.cursor()
    print("DB connection established")
    
    products = a[['country', 'tpnb']].drop_duplicates()
    products = products.groupby(["country"], observed=True)['tpnb'].apply(lambda s: s.tolist()).to_dict()
    print(f"Grouped products: {len(products)} countries")
    
    for key, value in products.items():
        print(f"Country {key}: {len([item for item in value if item])} products")
    
    all_results = []  # Store all query results
    print("Starting SQL queries...")
    
    for k, v in products.items():
        print(f"Processing country: {k} ({len(v)} TPNBs)")
        
        # Build tpnbs string once per country
        tpnbs = ",".join([str(x) for x in v if x])  # Filter out None/empty values
        
        sql = """ SELECT 
                        cntr_code AS country,
                        CAST(slad_tpnb AS INT) AS tpnb,
                        slad_tpn AS tpn,
                        slad_case_size AS case_capacity,
                        slad_net_weight AS unit_weight,
                        slad_net_weight * slad_case_size AS case_weight,
                        dmat_div_des_en AS DIV_DESC,
                        CAST(dmat_div_code AS INT) AS DIV_ID,
                        dmat_dep_des_en AS DEP_DESC,
                        CAST(dmat_dep_code AS INT) AS DEP_ID,
                        dmat_sec_des_en AS SEC_DESC,
                        CAST(dmat_sec_code AS INT) AS SEC_ID,
                        dmat_grp_des_en AS GRP_DESC,
                        CAST(dmat_grp_code AS INT) AS GRP_ID,
                        dmat_sgr_des_en AS SGR_DESC,
                        CAST(dmat_sgr_code AS INT) AS SGR_ID,
                        SUM(sunit.slsms_unit) AS sold_units
                FROM
                        DM.dim_artgld_details 
                JOIN 
                        dw.sl_sms sunit 
                        ON slad_dmat_id = sunit.slsms_dmat_id 
                        AND cntr_id = sunit.slsms_cntr_id
                WHERE 
                        slad_tpnb IN ({tpnbs}) 
                AND     
                        cntr_code = '{k}' 
                GROUP BY 
                        cntr_code,
                        slad_tpnb,
                        slad_tpn,
                        slad_case_size,
                        slad_net_weight,
                        dmat_div_des_en,
                        dmat_div_code,
                        dmat_dep_des_en,
                        dmat_dep_code,
                        dmat_sec_des_en,
                        dmat_sec_code,
                        dmat_grp_des_en,
                        dmat_grp_code,
                        dmat_sgr_des_en,
                        dmat_sgr_code
                """.format(tpnbs=tpnbs, k=k)
        
        query_result = pl.read_database(sql, conn)
        print(f"Query for {k}: {len(query_result)} rows returned")
        all_results.append(query_result)  # Add to list
    
    # Concatenate all results at once
    if all_results:
        df2 = pl.concat(all_results)
    else:
        df2 = pl.DataFrame()
    
    conn.close()  # Don't forget to close the connection
    print(f"Function complete: Returning {len(df2)} total rows")
    return df2.to_pandas()    

    
    
if missing_products:
    
    
    b =  missing_products(a)
    b.fillna(0, inplace=True)
    for x in [a,b]:
        x["tpnb"] = x["tpnb"].astype("int")
    
    c = a.merge(b, on=["country","tpnb"], how="left")
    c.fillna(0, inplace=True)
